{"name": "ssri", "version": "8.0.1", "description": "Standard Subresource Integrity library -- parses, serializes, generates, and verifies integrity metadata according to the SRI spec.", "main": "index.js", "files": [], "scripts": {"prerelease": "npm t", "postrelease": "npm publish", "prepublishOnly": "git push --follow-tags", "posttest": "npm run lint", "release": "standard-version -s", "test": "tap", "coverage": "tap", "lint": "standard"}, "tap": {"check-coverage": true}, "repository": "https://github.com/npm/ssri", "keywords": ["w3c", "web", "security", "integrity", "checksum", "hashing", "subresource integrity", "sri", "sri hash", "sri string", "sri generator", "html"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "twitter": "maybekatz"}, "license": "ISC", "dependencies": {"minipass": "^3.1.1"}, "devDependencies": {"standard": "^16.0.3", "standard-version": "^9.1.0", "tap": "^14.10.6"}, "engines": {"node": ">= 8"}}