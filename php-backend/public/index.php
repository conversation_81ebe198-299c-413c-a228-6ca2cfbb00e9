<?php
/**
 * PHP后台公共入口文件
 */

// 引入配置
require_once __DIR__ . '/../config/config.php';

// 获取请求URI
$requestUri = $_SERVER['REQUEST_URI'];
$path = parse_url($requestUri, PHP_URL_PATH);

// 处理API请求
if (strpos($path, '/api/') === 0) {
    require_once __DIR__ . '/../api/index.php';
    exit;
}

// 处理上传文件访问
if (strpos($path, '/uploads/') === 0) {
    $filename = basename($path);
    $filePath = UPLOAD_DIR . '/' . $filename;
    
    if (file_exists($filePath)) {
        $mimeType = mime_content_type($filePath);
        $fileSize = filesize($filePath);
        
        // 设置适当的头部
        header('Content-Type: ' . $mimeType);
        header('Content-Length: ' . $fileSize);
        header('Cache-Control: public, max-age=3600');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s', filemtime($filePath)) . ' GMT');
        
        // 支持范围请求（用于视频播放）
        if (isset($_SERVER['HTTP_RANGE'])) {
            handleRangeRequest($filePath, $fileSize, $mimeType);
        } else {
            readfile($filePath);
        }
        exit;
    } else {
        http_response_code(404);
        echo '文件不存在';
        exit;
    }
}

// 默认响应
http_response_code(200);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PHP后台服务</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .api-list {
            list-style: none;
            padding: 0;
        }
        .api-list li {
            margin: 10px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .method {
            font-weight: bold;
            color: #007bff;
            margin-right: 10px;
        }
        .endpoint {
            font-family: monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .description {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        .status {
            text-align: center;
            padding: 20px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            color: #155724;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="status">
            ✅ PHP后台服务运行正常<br>
            <small>服务时间: <?php echo date('Y-m-d H:i:s'); ?></small>
        </div>
        
        <h1>🚀 PHP后台API接口</h1>
        
        <ul class="api-list">
            <li>
                <span class="method">GET</span>
                <span class="endpoint">/api/health</span>
                <div class="description">健康检查接口</div>
            </li>
            
            <li>
                <span class="method">POST</span>
                <span class="endpoint">/api/admin/login</span>
                <div class="description">管理员登录接口</div>
            </li>
            
            <li>
                <span class="method">POST</span>
                <span class="endpoint">/api/upload</span>
                <div class="description">视频上传接口（小程序端）</div>
            </li>
            
            <li>
                <span class="method">GET</span>
                <span class="endpoint">/api/my/videos</span>
                <div class="description">获取用户视频列表（小程序端）</div>
            </li>
            
            <li>
                <span class="method">GET</span>
                <span class="endpoint">/api/admin/videos</span>
                <div class="description">获取所有视频列表（管理员端，需要认证）</div>
            </li>
            
            <li>
                <span class="method">GET</span>
                <span class="endpoint">/api/admin/stats</span>
                <div class="description">获取统计信息（管理员端，需要认证）</div>
            </li>
            
            <li>
                <span class="method">POST</span>
                <span class="endpoint">/api/admin/review</span>
                <div class="description">审核视频接口（管理员端，需要认证）</div>
            </li>
            
            <li>
                <span class="method">GET</span>
                <span class="endpoint">/api/admin/review-logs</span>
                <div class="description">获取审核记录（管理员端，需要认证）</div>
            </li>
            
            <li>
                <span class="method">GET</span>
                <span class="endpoint">/api/admin/settings</span>
                <div class="description">获取系统设置（管理员端，需要认证）</div>
            </li>
            
            <li>
                <span class="method">PUT</span>
                <span class="endpoint">/api/admin/settings</span>
                <div class="description">更新系统设置（超级管理员，需要认证）</div>
            </li>
            
            <li>
                <span class="method">GET</span>
                <span class="endpoint">/api/admin/expired-status</span>
                <div class="description">检查过期视频状态（管理员端，需要认证）</div>
            </li>
        </ul>
        
        <div style="margin-top: 30px; text-align: center; color: #666; font-size: 14px;">
            <p>默认管理员账号: admin / password</p>
            <p>认证Token: Bearer supersecrettoken</p>
        </div>
    </div>
</body>
</html>

<?php
/**
 * 处理HTTP范围请求（用于视频流播放）
 */
function handleRangeRequest($filePath, $fileSize, $mimeType) {
    $range = $_SERVER['HTTP_RANGE'];
    
    // 解析Range头
    if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
        $start = intval($matches[1]);
        $end = $matches[2] ? intval($matches[2]) : $fileSize - 1;
        
        // 确保范围有效
        $start = max(0, min($start, $fileSize - 1));
        $end = max($start, min($end, $fileSize - 1));
        
        $length = $end - $start + 1;
        
        // 设置206部分内容响应
        http_response_code(206);
        header('Content-Type: ' . $mimeType);
        header('Content-Length: ' . $length);
        header('Content-Range: bytes ' . $start . '-' . $end . '/' . $fileSize);
        header('Accept-Ranges: bytes');
        
        // 读取并输出指定范围的文件内容
        $file = fopen($filePath, 'rb');
        fseek($file, $start);
        
        $bufferSize = 8192;
        $remaining = $length;
        
        while ($remaining > 0 && !feof($file)) {
            $readSize = min($bufferSize, $remaining);
            echo fread($file, $readSize);
            $remaining -= $readSize;
            flush();
        }
        
        fclose($file);
    } else {
        // 无效的Range头，返回整个文件
        header('Content-Type: ' . $mimeType);
        header('Content-Length: ' . $fileSize);
        readfile($filePath);
    }
}
?>
