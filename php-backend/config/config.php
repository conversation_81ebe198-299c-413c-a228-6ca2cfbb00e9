<?php
/**
 * 系统配置文件
 */

// 基础配置
define('BASE_URL', $_ENV['BASE_URL'] ?? 'http://localhost:8000');
define('UPLOAD_DIR', __DIR__ . '/../uploads');
define('DATA_DIR', __DIR__ . '/../data');
define('LOG_DIR', __DIR__ . '/../logs');

// 数据库配置
define('DB_PATH', DATA_DIR . '/db.sqlite3');

// 认证配置
define('AUTH_TOKEN', 'supersecrettoken');

// 文件上传配置
define('MAX_FILE_SIZE', 100 * 1024 * 1024); // 100MB
define('ALLOWED_MIME_TYPES', [
    'video/mp4',
    'video/avi',
    'video/mov',
    'video/wmv',
    'video/flv',
    'video/webm',
    'video/mkv'
]);

// CORS配置
define('CORS_ORIGINS', ['*']);
define('CORS_METHODS', ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']);
define('CORS_HEADERS', ['Content-Type', 'Authorization', 'X-Requested-With']);

// 错误报告配置
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 确保必要的目录存在
function ensureDirectories() {
    $dirs = [UPLOAD_DIR, DATA_DIR, LOG_DIR];
    foreach ($dirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
}

// 日志函数
function logMessage($message, $level = 'INFO') {
    $timestamp = date('Y-m-d H:i:s');
    $logFile = LOG_DIR . '/app.log';
    $logEntry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

// 响应JSON数据
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// 错误响应
function errorResponse($message, $statusCode = 500, $code = null) {
    $response = ['message' => $message];
    if ($code) {
        $response['code'] = $code;
    }
    jsonResponse($response, $statusCode);
}

// 成功响应
function successResponse($message, $data = null) {
    $response = ['message' => $message];
    if ($data !== null) {
        $response['data'] = $data;
    }
    jsonResponse($response, 200);
}

// 获取客户端IP
function getClientIP() {
    $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

// 设置CORS头
function setCorsHeaders() {
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: ' . implode(', ', CORS_METHODS));
    header('Access-Control-Allow-Headers: ' . implode(', ', CORS_HEADERS));
    header('Access-Control-Max-Age: 86400');

    // 处理预检请求
    if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit;
    }
}

// 获取请求体JSON数据
function getJsonInput() {
    $input = file_get_contents('php://input');
    return json_decode($input, true);
}

// 验证必需参数
function validateRequired($data, $required) {
    $missing = [];
    foreach ($required as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            $missing[] = $field;
        }
    }
    
    if (!empty($missing)) {
        errorResponse('缺少必需参数: ' . implode(', ', $missing), 400);
    }
}

// 初始化
ensureDirectories();
setCorsHeaders();
