<?php
/**
 * 数据库配置文件
 */

class Database {
    private $pdo;
    private $dbPath;
    
    public function __construct($dbPath = null) {
        $this->dbPath = $dbPath ?: __DIR__ . '/../data/db.sqlite3';
        $this->connect();
        $this->initializeTables();
    }
    
    private function connect() {
        try {
            // 确保数据目录存在
            $dataDir = dirname($this->dbPath);
            if (!is_dir($dataDir)) {
                mkdir($dataDir, 0755, true);
            }
            
            $this->pdo = new PDO('sqlite:' . $this->dbPath);
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
            
            // 启用外键约束
            $this->pdo->exec('PRAGMA foreign_keys = ON');
            
            // 只在命令行模式下输出
            if (php_sapi_name() === 'cli') {
                echo "✅ 数据库连接成功\n";
            }
        } catch (PDOException $e) {
            die("❌ 数据库连接失败: " . $e->getMessage());
        }
    }
    
    public function getPdo() {
        return $this->pdo;
    }
    
    private function initializeTables() {
        try {
            // 创建videos表
            $this->createVideosTable();
            
            // 创建admins表
            $this->createAdminsTable();
            
            // 创建settings表
            $this->createSettingsTable();
            
            // 创建review_logs表
            $this->createReviewLogsTable();
            
            if (php_sapi_name() === 'cli') {
                echo "✅ 数据库表初始化完成\n";
            }
        } catch (PDOException $e) {
            if (php_sapi_name() === 'cli') {
                echo "❌ 数据库表初始化失败: " . $e->getMessage() . "\n";
            }
        }
    }
    
    private function createVideosTable() {
        $sql = "CREATE TABLE IF NOT EXISTS videos (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename TEXT NOT NULL,
            url TEXT NOT NULL,
            user_id TEXT NOT NULL,
            title TEXT,
            status TEXT DEFAULT 'pending',
            review_deadline_hours INTEGER DEFAULT 72,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        
        $this->pdo->exec($sql);
        if (php_sapi_name() === 'cli') {
            echo "✅ Videos表创建/检查完成\n";
        }
    }
    
    private function createAdminsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS admins (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT DEFAULT 'admin',
            active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        
        $this->pdo->exec($sql);
        
        // 检查是否已有默认管理员
        $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM admins WHERE username = ?");
        $stmt->execute(['admin']);
        $result = $stmt->fetch();
        
        if ($result['count'] == 0) {
            // 插入默认管理员
            $stmt = $this->pdo->prepare("INSERT INTO admins (username, password, role, active) VALUES (?, ?, ?, ?)");
            $stmt->execute(['admin', 'password', 'super_admin', 1]);
            if (php_sapi_name() === 'cli') {
                echo "✅ 默认管理员账号创建完成\n";
            }
        }

        if (php_sapi_name() === 'cli') {
            echo "✅ Admins表创建/检查完成\n";
        }
    }
    
    private function createSettingsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT UNIQUE NOT NULL,
            value TEXT,
            description TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        
        $this->pdo->exec($sql);
        
        // 插入默认设置
        $defaultSettings = [
            ['default_review_deadline_hours', '72', '默认审核时效(小时)'],
            ['auto_reject_expired', 'false', '是否自动拒绝过期视频'],
            ['block_upload_when_expired', 'false', '是否在有过期视频时阻止新上传']
        ];
        
        foreach ($defaultSettings as $setting) {
            $stmt = $this->pdo->prepare("INSERT OR IGNORE INTO settings (key, value, description) VALUES (?, ?, ?)");
            $stmt->execute($setting);
        }
        
        if (php_sapi_name() === 'cli') {
            echo "✅ Settings表创建/检查完成\n";
        }
    }
    
    private function createReviewLogsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS review_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            video_id INTEGER NOT NULL,
            reviewer_id INTEGER NOT NULL,
            reviewer_username TEXT NOT NULL,
            action TEXT NOT NULL,
            previous_status TEXT,
            new_status TEXT NOT NULL,
            comment TEXT,
            ip_address TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE,
            FOREIGN KEY (reviewer_id) REFERENCES admins(id) ON DELETE CASCADE
        )";
        
        $this->pdo->exec($sql);
        if (php_sapi_name() === 'cli') {
            echo "✅ Review_logs表创建/检查完成\n";
        }
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            throw new Exception("数据库查询失败: " . $e->getMessage());
        }
    }
    
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->query($sql, $data);
        
        return $this->pdo->lastInsertId();
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        $setParts = [];
        foreach (array_keys($data) as $key) {
            $setParts[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setParts);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        return $this->query($sql, $params);
    }
    
    public function select($table, $where = '', $params = [], $orderBy = '', $limit = '') {
        $sql = "SELECT * FROM {$table}";
        if ($where) $sql .= " WHERE {$where}";
        if ($orderBy) $sql .= " ORDER BY {$orderBy}";
        if ($limit) $sql .= " LIMIT {$limit}";
        
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    public function selectOne($table, $where = '', $params = []) {
        $sql = "SELECT * FROM {$table}";
        if ($where) $sql .= " WHERE {$where}";
        $sql .= " LIMIT 1";
        
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
}
