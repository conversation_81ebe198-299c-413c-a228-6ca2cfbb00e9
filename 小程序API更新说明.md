# 小程序API更新说明

## 概述

已将后台服务从Node.js迁移到PHP版本，小程序配置已相应更新。

## 更新内容

### 1. API地址更新

**开发环境**:
- 原地址: `http://localhost:3000`
- 新地址: `http://localhost:8000` ✅

**生产环境**:
- 地址保持不变: `http://video2.chinamarathonmajors.com.cn`
- 需要在服务器上部署PHP后台到此域名

### 2. API接口兼容性

所有API接口保持完全兼容，无需修改小程序代码：

| 接口 | 路径 | 方法 | 状态 |
|------|------|------|------|
| 健康检查 | `/api/health` | GET | ✅ 兼容 |
| 视频上传 | `/api/upload` | POST | ✅ 兼容 |
| 我的视频 | `/api/my/videos` | GET | ✅ 兼容 |

### 3. 请求格式兼容性

**视频上传接口**:
```javascript
// 请求格式保持不变
ApiService.uploadVideo(filePath, userId, title, onProgress)

// 后台接收参数:
// - videoFile: 视频文件
// - userId: 用户ID  
// - title: 视频标题
```

**获取视频列表**:
```javascript
// 请求格式保持不变
ApiService.getMyVideos(userId)

// 查询参数: ?userId=xxx
```

### 4. 响应格式兼容性

**上传成功响应**:
```json
{
  "message": "上传成功，等待审核",
  "data": {
    "videoId": 1,
    "filename": "videoFile-**********-123456789.mp4",
    "url": "http://localhost:8000/uploads/videoFile-**********-123456789.mp4"
  }
}
```

**视频列表响应**:
```json
[
  {
    "id": 1,
    "filename": "videoFile-**********-123456789.mp4",
    "url": "http://localhost:8000/uploads/videoFile-**********-123456789.mp4",
    "user_id": "user_**********_abcdefghi",
    "title": "我的视频",
    "status": "pending",
    "created_at": "2025-07-22 11:00:00",
    "updated_at": "2025-07-22 11:00:00"
  }
]
```

## 测试验证

### 1. 开发环境测试

确保PHP后台服务运行在8000端口：
```bash
cd php-backend
php -S localhost:8000 -t public
```

### 2. 小程序测试步骤

1. **健康检查测试**
   - 打开小程序开发者工具
   - 调用 `ApiService.checkHealth()`
   - 预期返回: `{"status":"ok","message":"服务器运行正常",...}`

2. **视频上传测试**
   - 选择视频文件
   - 调用 `ApiService.uploadVideo()`
   - 预期返回: 上传成功消息和视频信息

3. **视频列表测试**
   - 调用 `ApiService.getMyVideos(userId)`
   - 预期返回: 用户的视频列表数组

### 3. 网络请求调试

在小程序开发者工具中查看网络请求：
- 确认请求地址为 `http://localhost:8000/api/...`
- 确认响应状态码为 200
- 确认响应数据格式正确

## 部署注意事项

### 1. 生产环境部署

当部署到生产环境时，需要：

1. **部署PHP后台**到生产服务器
2. **配置域名**指向PHP后台
3. **更新小程序配置**（如果域名变更）
4. **测试所有接口**确保正常工作

### 2. 域名配置

如果生产环境域名需要更改，修改 `config.js`：
```javascript
production: {
  apiBase: 'https://your-new-domain.com',  // 新的PHP后台地址
  debug: false
}
```

### 3. HTTPS配置

生产环境建议使用HTTPS：
- 配置SSL证书
- 更新API地址为 `https://`
- 确保小程序域名白名单包含新域名

## 故障排除

### 1. 连接失败

如果小程序无法连接到后台：
- 检查PHP服务是否运行
- 检查端口是否正确（8000）
- 检查防火墙设置
- 查看小程序开发者工具的网络面板

### 2. 上传失败

如果视频上传失败：
- 检查PHP后台的uploads目录权限
- 检查PHP上传配置（upload_max_filesize等）
- 查看PHP后台日志文件

### 3. 数据不一致

如果数据显示异常：
- 检查数据库文件是否正确迁移
- 确认数据库表结构完整
- 运行PHP后台测试脚本验证

## 回滚方案

如果需要回滚到Node.js版本：

1. **停止PHP服务**
2. **启动Node.js服务**在3000端口
3. **修改config.js**:
   ```javascript
   development: {
     apiBase: 'http://localhost:3000',
     debug: true
   }
   ```
4. **重新测试**所有功能

## 联系支持

如果遇到问题，请：
1. 查看PHP后台日志: `php-backend/logs/app.log`
2. 查看小程序开发者工具控制台
3. 运行测试脚本: `php php-backend/test.php`
